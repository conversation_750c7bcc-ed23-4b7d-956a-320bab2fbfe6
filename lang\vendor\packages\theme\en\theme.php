<?php

return [
    'name' => 'Themes',
    'theme' => 'Theme',
    'author' => 'Author',
    'version' => 'Version',
    'description' => 'Description',
    'active_success' => 'Activate theme :name successfully!',
    'active' => 'Active',
    'activated' => 'Activated',
    'appearance' => 'Appearance',
    'theme_options' => 'Theme options',
    'save_changes' => 'Save Changes',
    'developer_mode' => 'Developer Mode Enabled',
    'custom_css' => 'Custom CSS',
    'custom_js' => 'Custom JS',
    'custom_header_js' => 'Header JS',
    'custom_header_js_placeholder' => 'JS in header of page, wrap it inside &#x3C;script&#x3E;&#x3C;/script&#x3E;',
    'custom_body_js' => 'Body JS',
    'custom_body_js_placeholder' => 'JS in body of page, wrap it inside &#x3C;script&#x3E;&#x3C;/script&#x3E;',
    'custom_footer_js' => 'Footer JS',
    'custom_footer_js_placeholder' => 'JS in footer of page, wrap it inside &#x3C;script&#x3E;&#x3C;/script&#x3E;',
    'custom_html' => 'Custom HTML',
    'custom_header_html' => 'Header HTML',
    'custom_header_html_placeholder' => 'HTML in header of page, no special tags: script, style, iframe...',
    'custom_body_html' => 'Body HTML',
    'custom_body_html_placeholder' => 'HTML in body of page, no special tags: script, style, iframe...',
    'custom_footer_html' => 'Footer HTML',
    'custom_footer_html_placeholder' => 'HTML in footer of page, no special tags: script, style, iframe...',
    'remove_theme_success' => 'Remove theme successfully!',
    'theme_is_not_existed' => 'This theme is not existed!',
    'remove' => 'Remove',
    'remove_theme' => 'Remove theme',
    'remove_theme_confirm_message' => 'Do you really want to remove this theme?',
    'remove_theme_confirm_yes' => 'Yes, remove it!',
    'total_themes' => 'Total themes',
    'show_admin_bar' => 'Show admin bar (When admin logged in, still show admin bar in website)?',
    'settings' => [
        'title' => 'Theme',
        'description' => 'Setting for theme',
        'redirect_404_to_homepage' => 'Redirect all not found requests to homepage?',
    ],
    'add_new' => 'Add new',
    'theme_activated_already' => 'Theme ":name" is activated already!',
    'missing_json_file' => 'Missing file theme.json!',
    'theme_invalid' => 'Theme is valid!',
    'published_assets_success' => 'Publish assets for :themes successfully!',
    'cannot_remove_theme' => 'Cannot remove activated theme, please activate another theme before removing ":name"!',
    'theme_deleted' => 'Theme ":name" has been destroyed.',
    'removed_assets' => 'Remove assets of a theme :name successfully!',
    'update_custom_css_success' => 'Update custom CSS successfully!',
    'update_custom_js_success' => 'Update custom JS successfully!',
    'update_custom_html_success' => 'Update custom HTML successfully!',
    'go_to_dashboard' => 'Go to dashboard',
    'custom_css_placeholder' => 'Using Ctrl + Space to autocomplete.',
    'theme_option_general' => 'General',
    'theme_option_general_description' => 'General settings',
    'theme_option_seo_open_graph_image' => 'SEO default Open Graph image',
    'theme_option_logo' => 'Logo',
    'theme_option_favicon' => 'Favicon',
    'folder_is_not_writeable' => 'Cannot write files! Folder :name is not writable. Please chmod to make it writable!',
];
