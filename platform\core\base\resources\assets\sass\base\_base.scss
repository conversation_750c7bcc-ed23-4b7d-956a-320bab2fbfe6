//## Dark blue theme color file
@import 'global/_variables';
@import 'global/components/mixins';
@import 'global/components/widget';
@import 'global/components/modal';
@import 'global/components/table';
@import 'global/components/other';
@import 'layouts/_variables';
@import 'libraries/libraries';

/***********
Page Header
***********/

/* Header search bar, toggler button & top menu */
.page-header.navbar {
    background-color: $header-bg-color;

    /* Top notification menu/bar */
    .top-menu {
        .navbar-nav {
            > li.dropdown {
                .dropdown-toggle {
                    > i {
                        color: $header-top-menu-icon-font-color;
                    }

                    .badge.badge-default {
                        background-color: $header-top-menu-badge-bg-color;
                        color: $header-top-menu-badge-font-color;
                    }

                    &:hover {
                        background-color: $header-top-menu-bg-hover-color;

                        > i {
                            color: lighten($header-top-menu-icon-font-color, 15%);
                        }
                    }
                }

                &.open {
                    .dropdown-toggle {
                        background-color: $header-top-menu-bg-hover-color-on-dropdown-open;

                        > i {
                            color: lighten($header-top-menu-icon-font-color, 15%);
                        }
                    }
                }
            }

            /* Extended Dropdowns */
            > li.dropdown-extended {

                .dropdown-menu {
                    border-color: $header-top-menu-extended-dropdown-border-color;

                    &:after {
                        border-bottom-color: $header-top-menu-extended-dropdown-header-bg-color;
                    }

                    > li.external {
                        background: $header-top-menu-extended-dropdown-header-bg-color;

                        > h3 {
                            color: $header-top-menu-extended-dropdown-header-font-color;
                        }

                        > a {
                            color: $link-color;

                            &:hover {
                                color: $link-hover-color;
                                text-decoration: none;
                            }
                        }
                    }

                    .dropdown-menu-list {
                        > li {
                            > a {
                                border-bottom: 1px solid $header-top-menu-extended-dropdown-item-border-color !important;
                                color: $header-top-menu-extended-dropdown-item-font-color;

                                &:hover {
                                    background: $header-top-menu-extended-dropdown-item-bg-color-on-hover;
                                }
                            }
                        }
                    }
                }
            }

            /* Notification */
            > li.dropdown-notification {
                .dropdown-menu {
                    .dropdown-menu-list {
                        > li {
                            > a {
                                .time {
                                    background: $header-top-menu-notification-time-bg-color;
                                }

                                &:hover {
                                    .time {
                                        background: darken($header-top-menu-notification-time-bg-color, 5%);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            /* Inbox */
            > li.dropdown-inbox {
                > .dropdown-toggle {
                    > .circle {
                        background-color: $brand-main-color;
                        color: $brand-main-font-color;
                    }

                    > .corner {
                        border-color: transparent transparent transparent $brand-main-color;
                    }
                }

                .dropdown-menu {
                    .dropdown-menu-list {
                        .subject {
                            .from {
                                color: $header-top-menu-inbox-dropdown-from-font-color;
                            }
                        }
                    }
                }
            }

            /* Tasks */
            > li.dropdown-tasks {

                .dropdown-menu {
                    .dropdown-menu-list {
                        .progress {
                            background-color: $header-top-menu-task-dropdown-progress-bg-color;
                        }
                    }
                }
            }

            /* User */
            > li.dropdown-user {
                > .dropdown-toggle {

                    > .username {
                        color: $header-top-menu-user-font-color;
                    }

                    > i {
                        color: $header-top-menu-user-font-color;
                    }
                }

                > .dropdown-menu {
                    width: 195px;
                }
            }

            /* Language */
            > li.dropdown-language {

                > .dropdown-toggle {
                    > .langname {
                        color: $header-top-menu-user-font-color;
                    }
                }
            }

            /* Dark version */
            > li.dropdown-dark {
                .dropdown-menu {
                    background: $header-top-menu-dropdown-dark-bg-color;
                    border: 0;

                    &:after {
                        border-bottom-color: $header-top-menu-dropdown-dark-bg-color;
                    }

                    > li.external {
                        background: $header-top-menu-dropdown-dark-header-bg-color;

                        > h3 {
                            color: $header-top-menu-dropdown-dark-header-font-color;
                        }

                        > a {
                            &:hover {
                                color: lighten($link-color, 11%);
                            }
                        }
                    }

                    &.dropdown-menu-default,
                    .dropdown-menu-list {
                        > li {
                            a {
                                color: $header-top-menu-dropdown-dark-item-font-color;
                                border-bottom: 1px solid $header-top-menu-dropdown-dark-item-border-color !important;

                                > i {
                                    color: $header-top-menu-dropdown-dark-item-icon-color;
                                }

                                &:hover {
                                    background: $header-top-menu-dropdown-dark-item-bg-color-on-hover;
                                }
                            }
                        }
                    }

                    &.dropdown-menu-default {
                        > li {
                            a {
                                border-bottom: 0 !important;
                            }

                            &.divider {
                                background: $header-top-menu-dropdown-dark-default-menu-divider;
                            }
                        }
                    }
                }
            }

            > li.dropdown-notification.dropdown-dark {
                .dropdown-menu {
                    .dropdown-menu-list {
                        > li {
                            > a {
                                .time {
                                    background: $header-top-menu-dropdown-dark-notification-time-bg-color;
                                }

                                &:hover {
                                    .time {
                                        background: darken($header-top-menu-dropdown-dark-notification-time-bg-color, 5%);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /* Toggler button for sidebar expand/collapse and responsive sidebar menu */
    .menu-toggler {
        @include burger-icon-color-change($header-toggler-color, $header-toggler-color);
    }
}

/* Page sidebar */

.page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover,
.page-sidebar {
    background-color: $bg-color;

    /* Default sidebar */
    .page-sidebar-menu {

        /* 1st level links */

        > li {
            > a {
                border-top: 1px solid $sidebar-menu-deliver-border-color;
                color: $sidebar-menu-link-font-color;

                > i {
                    color: $sidebar-menu-link-icon-font-color;
                }

                > i[class^="icon-"],
                > i[class*="icon-"] {
                    @if $theme-name == "light" {
                        color: darken($sidebar-menu-link-icon-font-color, 15%);
                    } @else if $theme-name == "light2" {
                        color: darken($sidebar-menu-link-icon-font-color, 15%);
                    } @else {
                        color: lighten($sidebar-menu-link-icon-font-color, 5%);
                    }
                }

                > .arrow {
                    &:before,
                    &.open:before {
                        color: $sidebar-menu-arrow-color;
                    }
                }
            }

            &.heading {
                > h3 {
                    color: darken($sidebar-menu-link-font-color, 23%);
                }
            }

            &:hover,
            &.open {
                > a {
                    background: $sidebar-menu-link-bg-color-on-hover;
                    color: $sidebar-menu-link-font-color-on-hover;

                    > i {
                        color: $sidebar-menu-link-icon-font-color-on-hover;
                    }

                    > .arrow {
                        &:before,
                        &.open:before {
                            color: $sidebar-menu-arrow-color-on-hover;
                        }
                    }
                }
            }

            &.active,
            &.active.open {
                > a {
                    background: $sidebar-menu-link-bg-color-on-active;
                    border-top-color: transparent;
                    color: $sidebar-menu-link-font-color-on-active;

                    &:hover {
                        background: $sidebar-menu-link-bg-color-on-active;
                    }

                    > i {
                        color: $sidebar-menu-link-icon-font-color-on-active;
                    }

                    > .arrow {
                        &:before,
                        &.open:before {
                            color: $sidebar-menu-arrow-color-on-active;
                        }
                    }
                }
            }

            &.active + li {
                > a {
                    border-top-color: transparent;
                }
            }

            &.active.open + li {
                > a {
                    border-top-color: $sidebar-menu-deliver-border-color;
                }
            }

            &:last-child {
                > a {
                    border-bottom: 1px solid transparent !important;
                }
            }
        }

        /* All links */

        li {
            > a {
                > .arrow {
                    &:before,
                    &.open:before {
                        color: $sidebar-menu-arrow-color;
                    }
                }
            }

            &:hover {
                > a {
                    > .arrow {
                        &:before,
                        &.open:before {
                            color: $sidebar-menu-arrow-color-on-hover;
                        }
                    }
                }
            }

            &.active {
                > a {
                    > .arrow {
                        &:before,
                        &.open:before {
                            color: $sidebar-menu-arrow-color-on-active;
                        }
                    }
                }
            }
        }

        .page-sidebar-closed &:hover {
            .sub-menu {
                background-color: $bg-color;
            }
        }

        .sub-menu {
            > li {
                > a {
                    color: $sidebar-menu-sub-menu-link-font-color;

                    > i {
                        color: $sidebar-menu-sub-menu-link-icon-font-color;
                    }

                    > i[class^="icon-"],
                    > i[class*="icon-"] {
                        @if $theme-name == "light" {
                            color: darken($sidebar-menu-link-icon-font-color, 15%);
                        } @else if $theme-name == "light2" {
                            color: darken($sidebar-menu-link-icon-font-color, 15%);
                        } @else {
                            color: lighten($sidebar-menu-link-icon-font-color, 5%);
                        }
                    }

                    > .arrow {
                        &:before,
                        &.open:before {
                            color: $sidebar-menu-arrow-color;
                        }
                    }
                }

                &:hover,
                &.open,
                &.active {
                    > a {
                        background: $sidebar-menu-sub-menu-link-bg-color-on-hover !important;

                        > i {
                            color: $sidebar-menu-sub-menu-link-icon-font-color-on-hover;
                            @if $theme-name == "light" {
                                color: darken($sidebar-menu-link-icon-font-color, 15%);
                            } @else if $theme-name == "light2" {
                                color: darken($sidebar-menu-link-icon-font-color, 15%);
                            } @else {
                                color: lighten($sidebar-menu-link-icon-font-color, 20%);
                            }
                        }

                        > .arrow {
                            &:before,
                            &.open:before {
                                color: $sidebar-menu-arrow-color-on-hover;
                            }
                        }
                    }
                }
            }
        }
    }

    /* light sidebar */
    .page-sidebar-menu.page-sidebar-menu-light {

        /* 1st level links */
        > li {
            &:hover,
            &.open {
                > a {
                    background: $light-sidebar-menu-link-bg-color-on-hover;
                }
            }

            &.active,
            &.active.open {
                > a {
                    background: $light-sidebar-menu-link-bg-color-on-active;
                    border-left: 4px solid $light-sidebar-menu-link-border-color;
                    color: $light-sidebar-menu-link-font-color-on-active;

                    &:hover {
                        border-left: 4px solid $sidebar-menu-link-bg-color-on-active;
                        background: $light-sidebar-menu-link-bg-color-on-hover;
                    }

                    > i {
                        color: $light-sidebar-menu-link-icon-color-on-active;
                    }

                    > .arrow {
                        &:before,
                        &.open:before {
                            color: $light-sidebar-menu-link-arrow-color-on-active;
                        }
                    }
                }
            }

            .sub-menu {
                background: $light-sidebar-menu-sub-menu-bg-color;

                > li {
                    &:hover,
                    &.open,
                    &.active {
                        > a {
                            background: $light-sidebar-menu-sub-menu-link-bg-color-on-hover !important;
                        }
                    }
                }
            }
        }
    }

    .sidebar-toggler {
        @include burger-icon-color-change($header-toggler-color, $header-toggler-color);

        background: $header-toggler-bg-color;

        &:hover {
            background: darken($header-toggler-bg-color, 5%);
        }
    }

    /* Sidebar search */
}

.page-sidebar-reversed {

    .page-sidebar-menu.page-sidebar-menu-light {
        /* 1st level links */
        > li {
            &.active,
            &.active.open {
                > a {
                    border-left: 0;
                    border-right: 4px solid $light-sidebar-menu-link-border-color;

                    &:hover {
                        border-left: 0;
                        border-right: 4px solid $sidebar-menu-link-bg-color-on-active;
                    }
                }
            }
        }
    }
}

/******
Page Footer
******/

.page-footer {
    .page-footer-inner {
        color: $footer-default-font-color;
    }

    .page-footer-fixed & {
        background-color: $footer-fixed-bg-color;
    }
}

@media (min-width: $screen-md-min) { /* 992px */

    /* Sidebar menu closed */
    .page-sidebar-menu.page-sidebar-menu-hover-submenu {
        > li:hover {
            > .sub-menu {
                box-shadow: 5px 5px rgba($sidebar-menu-sub-menu-box-shadow-color, 0.2);

                &.sidebar-toggler-wrapper {
                    box-shadow: none;
                }
            }
        }
    }

    .page-sidebar-menu.page-sidebar-menu-closed {
        > li:hover {
            box-shadow: 5px 5px rgba($sidebar-menu-sub-menu-box-shadow-color, 0.2);

            &.sidebar-toggler-wrapper {
                box-shadow: none;
            }

            > .sub-menu {
                box-shadow: 5px 5px rgba($sidebar-menu-sub-menu-box-shadow-color, 0.2);

                &.sidebar-toggler-wrapper {
                    box-shadow: none;
                }
            }
        }
    }

    /* Light sidebar menu */
    .page-sidebar-menu.page-sidebar-menu-light.page-sidebar-menu-closed {
        > li.heading {
            padding: 0;
            margin-top: 15px;
            margin-bottom: 15px;
            border-top: 1px solid $sidebar-menu-deliver-border-color !important;
        }
    }

    /* Fixed Sidebar */

    .page-sidebar-fixed:not(.page-footer-fixed) {
        .page-content {
            border-bottom: 0;
        }

        .page-footer {
            background-color: #ffffff;

            .page-footer-inner {
                color: #333333;
            }
        }
    }

    /* Sidebar Menu Wirh Hoverable Submenu */

    .page-sidebar-menu-hover-submenu {
        li {
            &:hover {
                a {
                    > .arrow {
                        border-right: 8px solid $sidebar-menu-hover-sub-menu-bg-color;

                        .page-sidebar-reversed & {
                            border-left: 8px solid $sidebar-menu-hover-sub-menu-bg-color;
                        }
                    }
                }

                > .sub-menu {
                    background: $sidebar-menu-hover-sub-menu-bg-color !important;
                }
            }
        }
    }
}

@media (max-width: $screen-sm-max) { /* 991px */
    /* Page sidebar */
    .page-sidebar {
        background-color: $mobile-sidebar-menu-bg-color;

        .page-sidebar-menu {
            > li {
                > a {
                    border-top: 1px solid $bg-color;
                }

                &:hover,
                &.open {
                    > a {
                        background: $mobile-sidebar-menu-link-bg-color-on-hover;
                    }
                }

                &:last-child {
                    > a {
                        border-bottom: 0 !important;
                    }
                }

                .sub-menu {
                    background-color: $mobile-sidebar-menu-bg-color !important;
                }
            }

        }

        /* light sidebar */
        .page-sidebar-menu.page-sidebar-menu-light {

            /* 1st level links */
            > li {
                &:hover,
                &.open {
                    > a {
                        background: $mobile-light-sidebar-menu-link-bg-color-on-hover;
                    }
                }

                &.active,
                &.active.open {
                    > a {
                        background: $mobile-light-sidebar-menu-link-bg-color-on-active;

                        &:hover {
                            background: $mobile-light-sidebar-menu-link-bg-color-on-active;
                        }
                    }
                }

                .sub-menu {
                    background: $mobile-light-sidebar-menu-sub-menu-bg-color !important;

                    > li {
                        &:hover,
                        &.open,
                        &.active {
                            > a {
                                background: $mobile-light-sidebar-menu-sub-menu-link-bg-color-on-hover !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: $screen-xs-min) { /* 480px */

    .page-header.navbar {
        .top-menu {
            background-color: $bg-color;

            .page-header-fixed-mobile & {
                background-color: $header-bg-color;
            }

            .navbar-nav {
                > li.dropdown-user {
                    .dropdown-toggle {
                        .page-header-fixed-mobile & {
                            background: none;
                        }
                    }
                }
            }
        }
    }
}

.page-wrapper {
    background-color: $bg-color;
}

.page-spinner-bar > div,
.block-spinner-bar > div {
    background: lighten($brand-main-color, 5%);
}
