.table {
    margin-bottom: 0;

    &.table-bordered {
        thead > tr > th {
            border-bottom: 0;
        }
    }

    &.table-hover > tbody > tr:hover,
    &.table-hover > tbody > tr:hover > td {
        background: lighten(#94a0b2, 32%) !important;
    }

    .btn {
        margin-top: 0;
        margin-left: 0;
        margin-right: 5px;
    }

    td .img-responsive {
        width: 100%;
    }

    th {
        color: #afafaf;
        border-bottom: none !important;
        text-align: left;
        padding-left: 18px;
        font-weight: normal;
        font-size: 11px;
        text-transform: uppercase;

        &.no-sort {
            padding-right: 0 !important;

            &:before, &:after {
                display: none !important;
            }
        }

        &:hover {
            color: #333333;
        }
    }

    .text-start {
        text-align: left;
    }

    .text-center {
        text-align: center;
    }

    td {
        .text-start {
            display: block;
            float: left !important;
        }

        padding: 5px 12px !important;
    }
}

.tableFloatingHeaderOriginal {
    left: 0 !important;
    top: 0;
}

.table-wrapper {
    .portlet.portlet-no-padding {
        padding: 0;

        .portlet-title {
            min-height: 55px;
            margin-bottom: 0;
            padding-left: 11px;
        }

        .portlet-body {
            padding-top: 0;
        }
    }
}
