(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,a=void 0,a=function(e,i){if("object"!==t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,i||"default");if("object"!==t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(e)}(o,"string"),"symbol"===t(a)?a:String(a)),r)}var o,a}var i=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var i,n,r;return i=t,(n=[{key:"init",value:function(){$(document).find(".tags").each((function(t,e){var i=new Tagify(e,{keepInvalidTags:void 0===$(e).data("keep-invalid-tags")||$(e).data("keep-invalid-tags"),enforceWhitelist:void 0!==$(e).data("enforce-whitelist")&&$(e).data("enforce-whitelist"),delimiters:void 0!==$(e).data("delimiters")?$(e).data("delimiters"):",",whitelist:e.value.trim().split(/\s*,\s*/),userInput:void 0===$(e).data("user-input")||$(e).data("user-input")});$(e).data("url")&&i.on("input",(function(t){i.settings.whitelist.length=0,i.loading(!0).dropdown.hide.call(i),$.ajax({type:"GET",url:$(e).data("url"),success:function(e){i.settings.whitelist=e,i.loading(!1).dropdown.show.call(i,t.detail.value)}})}))}))}}])&&e(i.prototype,n),r&&e(i,r),Object.defineProperty(i,"prototype",{writable:!1}),t}();$(document).ready((function(){(new i).init()}))})();