.form-actions {
    &.form-actions-default {
        margin-top: 0;

        &.action-vertical {
            display: flex;
            width: 100%;
            flex-direction: row;
            align-items: center;

            .widget-title {
                flex: 1;
                border: 0 none;
                font-size: 16px !important;
                font-weight: 700 !important;
                color: #666666 !important;
                text-transform: uppercase !important;
                padding: 15px !important;
                height: auto !important;

                > h4 > span {
                    margin: 0 !important;
                }
            }
        }

        &.action-horizontal {
            .widget-body {
                .btn {
                    margin: 5px 0;
                }
            }
        }
    }

    &.form-actions-fixed-top {
        position: fixed;
        top: 0;
        left: 235px;
        right: 0;
        z-index: 999;
        background-color: #ffffff;
        border-bottom: 1px solid #cccccc;
        box-shadow: 0 2px 2px -2px rgba(0, 0, 0, .5);
        text-align: right;
        padding: 5px 20px;

        .btn {
            margin-left: 5px;
            margin-top: 5px;
        }
    }
}

.page-sidebar-closed {
    .form-actions {
        &.form-actions-fixed-top {
            left: 45px;
        }
    }
}

.top-menu {
    .dropdown-menu {
        .dropdown-menu-list {
            &.scroller {
                max-height: 400px;
                overflow-y: scroll;
            }
        }
    }
}

.multi-choices-widget {
    ul {
        list-style: none;
        margin: 0;
        padding: 0 0 0 5px;

        li {
            display: block;

            .invalid-feedback {
                position: absolute;
                bottom: 0;
                left: 0;
            }
        }

        ul {
            padding-left: 18px;
        }
    }
}

.meta-boxes {
    .mt-radio-list {
        label {
            display: block;
        }
    }
}

.img-thumbnail-wrap {
    width: 100%;
    height: 300px;
    overflow-y: hidden;

    img {
        width: 100%;
    }
}

.editor-action-item {
    float: left;
    margin-right: 10px;
    margin-bottom: 5px;
}

.dashboard_widget_msg {
    padding: 40px 20px;
    text-align: center;
    color: #72777c;
    font-size: 14px;
    width: 100%;

    .smiley:before {
        content: "\f118";
        font: 400 120px/1 Font Awesome\ 5 Free;
        speak: none;
        display: block;
        margin: 0 5px 0 0;
        padding: 0;
        text-indent: 0;
        text-align: center;
        position: relative;
        -webkit-font-smoothing: antialiased;
        text-decoration: none !important;
    }
}

.page-content {
    .breadcrumb {
        margin-bottom: 20px;
    }

    .table-language {
        margin-top: 10px;
    }
}

.tableFloatingHeaderOriginal {
    background: #ffffff;
}

.box_img_sale {
    background: url('#{$general-img-path}img.png') -135px -110px #7c87b6;
    width: 45px;
    height: 45px;
    margin: 0;
    float: left;
}

.input-group-text {
    .btn {
        width: 34px;
        text-align: center;
        padding: 6px 0;
    }
}

.input-group .form-control.datepicker {
    position: static;
}

.btn-choose-file .btn {
    padding: 6px 10px;
    width: auto !important;
}

.portlet-body.widget-content {
    min-height: 200px;
}

.page-sidebar {
    .sidebar-content {
        .badge {
            padding: 4px 6px;
        }

        .nav-item.active {
            .badge {
                background: #ffffff;
                color: #333333;
            }
        }
    }
}
