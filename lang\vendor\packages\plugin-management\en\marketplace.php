<?php

return [
    'previous' => '&laquo; Previous',
    'next' => 'Next &raquo;',
    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'results' => 'results',
    'add_new' => 'Add new',
    'installed' => 'Installed',
    'install_now' => 'Install now',
    'installing' => 'Installing...',
    'detail' => 'Details',
    'version' => 'Version',
    'minimum_core_version' => 'Minimum core version',
    'last_update' => 'Last update',
    'compatible_version' => 'Compatible with your version of Botble',
    'incompatible_version' => 'Incompatible with your version of Botble',
    'deactivate' => 'Deactivate',
    'activate' => 'Activate',
    'activating' => 'Activating...',
    'activated' => 'Activated',
    'connection_aborted' => 'Connection Aborted',
    'connection_aborted_description' => 'Connection Aborted Description',
    'api_connect_error' => 'Connect to Marketplace API Error, please contact support',
    'keyword' => 'Keyword',
    'search' => 'Search',
    'all' => 'All',
    'featured' => 'Featured',
    'popular' => 'Popular',
    'top_rated' => 'Top Rated',
    'install_plugin' => 'Install plugin',
    'cancel' => 'Cancel',
    'yes_install' => ' Yes! Install',
    'message_alert' => 'Are you sure you want to install this plugin?',
    'folder_permissions' => 'Folder does not have permission to write file or the update file path could not be resolved, please contact support',
    'unzip_failed' => 'Unzip extraction failed',
    'unzip_success' => 'Download file extracted',
    'install_success' => 'Installed plugin successfully!',
    'update_success' => 'Updated plugin successfully!',
    'minimum_core_version_error' => 'Cannot install this plugin. Minimum core version is :version.',
];
