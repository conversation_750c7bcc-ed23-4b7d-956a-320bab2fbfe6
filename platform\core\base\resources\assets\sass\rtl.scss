body[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

.btn {
    + {
        .btn {
            margin-right: 10px;
            margin-left: 0;
        }
    }

    i {
        margin-right: 0;
        display: inline-block;
        margin-left: 5px;
    }
}

.text-start {
    text-align: right !important;
}

.text-end {
    text-align: left !important;
}

.float-start {
    float: right !important;
}

.float-end {
    float: left !important;
}

.page-header.navbar {
    .page-logo {
        float: right;
        padding-left: 0;
        padding-right: 20px;
    }

    .top-menu {
        float: left;
    }
}

.breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
}

.breadcrumb > li:first-child:before {
    padding-right: 0;
    padding-left: 4px;
}

.breadcrumb-item + .breadcrumb-item {
    padding-left: 0;
    padding-right: 0.5rem;
}

.breadcrumb {
    float: right;
}

.page-sidebar .page-sidebar-menu > li > a > i, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu > li > a > i {
    margin-right: 0;
    margin-left: 5px;
}

input[type=radio], input[type=checkbox] {
    margin: 0 0 0 0.5rem;
}

input[type=radio]:checked:before {
    left: 0;
    right: 2px;
}

input[type=radio]:checked:after {
    left: 0;
    right: -1px;
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    right: 1px;
    left: auto;
}

.ui-select-wrapper .svg-next-icon, .ui-select-wrapper .next-icon-text {
    right: auto;
    left: 7px;
}

.page-sidebar .page-sidebar-menu li > a > .arrow:before, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu li > a > .arrow:before {
    float: left;
    right: auto;
    left: 14px;
}

.page-footer {
    margin-left: 0;
    margin-right: 235px;
}

.page-header.navbar .page-logo > .logo-image, .page-header.navbar .page-logo > a {
    float: right;
}

.page-header.navbar .menu-toggler.sidebar-toggler {
    float: left;
}

.page-sidebar-closed.page-sidebar-closed-hide-logo .page-header.navbar .menu-toggler.sidebar-toggler {
    margin-right: 0;
    margin-left: 13px;
}

.portlet > .portlet-title > .caption {
    float: right;
}

.portlet.portlet-no-padding .portlet-title {
    padding-left: 0;
    padding-right: 11px;
}

.portlet.portlet-no-padding .portlet-title .tools {
    float: left;
    margin-right: 0;
    margin-left: 10px;
}

.widget_footer .page_previous {
    margin-left: -18px;
    margin-right: 20px;
    background-position: -54px -203px;
}

.widget_footer .page_next {
    margin-left: 10px !important;
    margin-right: 20px !important;
    background-position: -10px -203px;
}

.widget_pagination {
    text-align: left;
}

.portlet-body ul.item-list {
    padding: 0 7px 0 0;
}

.log-icon {
    float: right;
    margin: 5px 0 0 5px;
}

.manage-widget {
    i {
        float: left;
        margin-right: 5px;
        margin-top: 3px;
    }
}

.modal-header {
    h4 {
        .til_img {
            float: right;
            margin: 0 5px 0 0;
        }
    }
}

.modal-header strong, .modal-header h5.modal-title {
    float: right;
    margin: 0 15px 0 0;
}

.page-content {
    .dataTables_wrapper {
        .dt-buttons {
            margin-right: 0;
            margin-left: 10px;
            right: auto;
            left: 0;
        }
    }
}

.dataTables_filter {
    float: right;
}

.btn {
    i {
        margin-left: 0;
    }
}

div.dataTables_wrapper {
    div.dataTables_filter {
        left: auto;
        right: 150px;
    }
}

.table-has-actions.table-has-filter {
    .dataTables_filter {
        left: auto;
        right: 250px;
    }
}

.dataTables_wrapper {
    thead {
        .table-check-all {
            margin-left: 0;
            margin-right: 5px;
        }
    }
}

.table {
    th.no-sort {
        padding-right: 8px !important;
        padding-left: 0 !important;
    }
}

.page-content .dataTables_wrapper .dataTables_info {
    margin-left: 0;
    margin-right: 10px;
}

.widget-title > h4 {
    float: right;
}

#seo_wrap .btn-trigger-show-seo-detail {
    right: auto;
    left: 20px;
}

.rv-media-header .rv-media-top-header .rv-media-search .btn {
    right: auto !important;
    left: 0;
}

.form-group .onoffswitch {
    float: right;
    margin-right: 0;
    margin-left: 10px;
}

.portlet.light > .portlet-title > .caption > i {
    float: right;
    margin-right: 0;
    margin-left: 5px;
}

.table th {
    text-align: right;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown .dropdown-menu {
    float: right;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-user .dropdown-menu > li > a {
    text-align: right;
}

.page-header.navbar .top-menu .navbar-nav {
    margin-right: 0;
    margin-left: 20px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown .dropdown-menu {
    float: right;
    left: 0;
    right: auto;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown .dropdown-menu:before {
    right: auto;
    left: 9px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown .dropdown-menu:after {
    right: auto;
    left: 10px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-extended .dropdown-menu > li.external > h3 {
    float: right;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-extended .dropdown-menu > li.external > a {
    right: auto;
    left: 10px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-inbox > .dropdown-menu .dropdown-menu-list > li .photo {
    float: right;
    margin: 0 0 6px 6px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-inbox > .dropdown-menu .dropdown-menu-list > li .subject,
.page-header.navbar .top-menu .navbar-nav > li.dropdown-inbox > .dropdown-menu .dropdown-menu-list > li .message {
    margin-left: 0;
    margin-right: 46px;
    text-align: right;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-inbox > .dropdown-menu .dropdown-menu-list > li .subject {
    float: right;
    margin-left: 5px;
    margin-top: 5px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-inbox > .dropdown-menu .dropdown-menu-list > span {
    display: inline-block;
}

.rv-media-container .rv-media-details {
    border-left: none;
    border-right: 1px solid #dbdbdb;
}

.rv-media-grid li {
    float: right !important;
}

.rv-media-list .rv-media-list-title .rv-media-file-name i {
    margin-right: 0;
    margin-left: 5px;
    vertical-align: middle;
}

.dropdown-menu > li > a {
    text-align: right;
}

.dropdown > .dropdown-menu:before, .dropdown-toggle > .dropdown-menu:before, .btn-group > .dropdown-menu:before {
    left: auto;
    right: 9px;
}

.dropdown > .dropdown-menu:after, .dropdown-toggle > .dropdown-menu:after, .btn-group > .dropdown-menu:after {
    left: auto;
    right: 10px;
}

.page-content .rv-media-container .rv-modals .modal-header .close {
    margin: 8px 0 0 15px !important;
}

.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {
    left: auto;
    right: 0;
}

.dropdown-hover a {
    text-align: right;
}

.dropdown-hover a > i {
    right: auto;
    left: 5px;

    &:before {
        content: "\f104";
    }
}

.dropdown-hover {
    .dropdown-content {
        left: auto;
        right: 100%;
        text-align: right;
    }
}

.btn-group > .btn:not(:first-child), .btn-group > .btn-group:not(:first-child) > .btn {
    border-radius: 3px 0 0 3px;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .btn-group:not(:last-child) > .btn {
    border-radius: 0 3px 3px 0;
}

.page-content {
    .dataTables_wrapper {
        .btn {
            margin: 0 0 10px 10px;
        }
    }
}

.tagify__tag {
    margin: 5px 5px 5px 0;
}

.tagify__tag__removeBtn {
    margin-left: 4.66667px;
    margin-right: -4.66667px;
}

.form-actions.form-actions-fixed-top {
    left: 0;
    right: 235px;
    text-align: left;
}

.form-actions.form-actions-fixed-top {
    .btn {
        margin-right: 0;
        margin-left: 5px;
    }
}

.page-sidebar-closed {
    .form-actions {
        &.form-actions-fixed-top {
            right: 45px;
        }
    }
}

small.charcounter {
    right: auto;
    left: 0;
}

.widget-title > h4 span, .widget-title > h4 label {
    margin-left: 0;
    margin-right: 10px;
}

.note {
    border-left: none;
    border-right: 5px solid #eeeeee;
    padding: 15px 15px 15px 30px;
    border-radius: 4px 0 0 4px;
}

@media (min-width: 992px) {
    .page-sidebar {
        float: right;
        margin-right: 0;
        margin-left: -100%;
    }

    .page-content-wrapper {
        float: right;

        .page-content {
            margin-left: 0;
            margin-right: 235px;
        }
    }

    .page-sidebar-closed {
        .page-content-wrapper {
            .page-content {
                margin-left: 0 !important;
                margin-right: 45px !important;
            }
        }
    }

    .scroll-to-top {
        right: auto;
        left: 20px;
    }
}

.toast-bottom-right {
    right: auto;
    left: 0;

    .toast-close-button {
        right: auto;
        left: -.3em;
        float: left;
    }
}

#toast-container {
    > div {
        padding: 15px 50px 15px 15px;
        background-position: right 15px center;
    }
}

.app-grid--blank-slate .app-item .app-footer {
    padding-left: 0;
    padding-right: 10px;
}

.dd3-handle {
    left: auto;
    right: 0;
}

.dd3-content {
    .show-item-details {
        left: auto;
        right: 319px;
        border-left: none;
        border-right: 1px solid #aaa;
    }

    > span.text.float-end {
        margin-left: 25px;
        margin-right: 0;
    }
}

.widget-menu {
    .widget {
        &.meta-boxes {
            .btn-add-to-menu {
                i {
                    display: inline;
                }
            }

            .narrow-icon {
                float: left;
            }
        }
    }
}
