<?php

return [
    'customer_new_order_title' => '<PERSON><PERSON><PERSON> nhận đơn hàng',
    'customer_new_order_description' => '<PERSON><PERSON><PERSON><PERSON> gửi khi khách hàng tạo đơn hàng',
    'order_cancellation_title' => 'Hủy đơn hàng',
    'order_cancellation_description' => '<PERSON><PERSON><PERSON><PERSON> gửi khi khách hàng hủy đơn hàng',
    'delivery_confirmation_title' => '<PERSON>ác nhận giao hàng',
    'delivery_confirmation_description' => '<PERSON><PERSON><PERSON><PERSON> gửi đến khách hàng khi bắt đầu giao hàng',
    'admin_new_order_title' => 'Thông báo có đơn hàng mới',
    'admin_new_order_description' => 'Đ<PERSON><PERSON>c gửi cho quản trị viên khi có khách mua hàng',
    'order_confirmation_title' => '<PERSON><PERSON>c nhận đơn hàng',
    'order_confirmation_description' => 'Email đ<PERSON> gửi đến khách hàng khi đơn hàng x<PERSON>c nh<PERSON>',
    'payment_confirmation_title' => '<PERSON><PERSON><PERSON> nhận thanh toán',
    'payment_confirmation_description' => 'Email được gửi đến khách hàng khi đơn hàng xác nhận thanh toán',
    'order_recover_title' => 'Đơn hàng đang chờ hoàn tất',
    'order_recover_description' => 'Email nhắc nhở khách hàng hoàn tất đơn hàng',
    'link_go_to_our_shop' => 'hoặc <a href=":link">Truy cập website</a>',
    'order_information' => 'Thông tin đơn hàng',
    'order_number' => 'Mã đơn hàng: <strong>:order_id</strong>',
    'view_order' => 'Xem đơn hàng',
];
