/*!
 * Cropper v0.7.1
 * https://github.com/fengyuanchen/cropper
 *
 * Copyright 2014 <PERSON><PERSON>
 * Released under the MIT license
 */
.cropper-container {
    position: relative;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;

    img {
        width: 100%;
        max-width: none !important;
        height: 100%;
        max-height: none !important;
    }
}

.cropper-modal, .cropper-canvas {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.cropper-canvas {
    background-color: #ffffff;
    filter: alpha(opacity=0);
    opacity: 0;
}

.cropper-modal {
    background-color: #000000;
    filter: alpha(opacity=50);
    opacity: .5;
}

.cropper-dragger {
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
}

.cropper-viewer {
    display: block;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline-width: 1px;
    outline-style: solid;
    outline-color: #6699ff;
    outline-color: rgba(51, 102, 255, .75);
}

.cropper-dashed {
    position: absolute;
    display: block;
    filter: alpha(opacity=50);
    border: 0 dashed #ffffff;
    opacity: .5;

    &.dashed-h {
        top: 33.3%;
        left: 0;
        width: 100%;
        height: 33.3%;
        border-top-width: 1px;
        border-bottom-width: 1px;
    }

    &.dashed-v {
        top: 0;
        left: 33.3%;
        width: 33.3%;
        height: 100%;
        border-right-width: 1px;
        border-left-width: 1px;
    }
}

.cropper-face, .cropper-line, .cropper-point {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    filter: alpha(opacity=10);
    opacity: .1;
}

.cropper-face {
    top: 0;
    left: 0;
    cursor: move;
    background-color: #ffffff;
}

.cropper-line {
    background-color: #6699ff;

    &.line-e {
        top: 0;
        right: -3px;
        width: 5px;
        cursor: e-resize;
    }

    &.line-n {
        top: -3px;
        left: 0;
        height: 5px;
        cursor: n-resize;
    }

    &.line-w {
        top: 0;
        left: -3px;
        width: 5px;
        cursor: w-resize;
    }

    &.line-s {
        bottom: -3px;
        left: 0;
        height: 5px;
        cursor: s-resize;
    }
}

.cropper-point {
    width: 5px;
    height: 5px;
    background-color: #6699ff;
    filter: alpha(opacity=75);
    opacity: .75;

    &.point-e {
        top: 50%;
        right: -3px;
        margin-top: -3px;
        cursor: e-resize;
    }

    &.point-n {
        top: -3px;
        left: 50%;
        margin-left: -3px;
        cursor: n-resize;
    }

    &.point-w {
        top: 50%;
        left: -3px;
        margin-top: -3px;
        cursor: w-resize;
    }

    &.point-s {
        bottom: -3px;
        left: 50%;
        margin-left: -3px;
        cursor: s-resize;
    }

    &.point-ne {
        top: -3px;
        right: -3px;
        cursor: ne-resize;
    }

    &.point-nw {
        top: -3px;
        left: -3px;
        cursor: nw-resize;
    }

    &.point-sw {
        bottom: -3px;
        left: -3px;
        cursor: sw-resize;
    }

    &.point-se {
        right: -3px;
        bottom: -3px;
        width: 20px;
        height: 20px;
        cursor: se-resize;
        filter: alpha(opacity=100);
        opacity: 1;

        &:before {
            position: absolute;
            right: -50%;
            bottom: -50%;
            display: block;
            width: 200%;
            height: 200%;
            content: " ";
            background-color: #6699ff;
            filter: alpha(opacity=0);
            opacity: 0;
        }
    }
}

@media (min-width: 768px) {
    .cropper-point {
        &.point-se {
            width: 15px;
            height: 15px;
        }
    }
}

@media (min-width: 992px) {
    .cropper-point {
        &.point-se {
            width: 10px;
            height: 10px;
        }
    }
}

@media (min-width: 1200px) {
    .cropper-point {
        &.point-se {
            width: 5px;
            height: 5px;
            filter: alpha(opacity=75);
            opacity: .75;
        }
    }
}

.cropper-hidden {
    display: none !important;
}

.cropper-invisible {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: auto !important;
    max-width: none !important;
    height: auto !important;
    max-height: none !important;
    filter: alpha(opacity=0);
    opacity: 0;
}

.cropper-move {
    cursor: move;
}

.cropper-crop {
    cursor: crosshair;
}

.cropper-disabled .cropper-canvas, .cropper-disabled .cropper-face, .cropper-disabled .cropper-line, .cropper-disabled .cropper-point {
    cursor: not-allowed;
}

.avatar-body {
    padding-right: 15px;
    padding-left: 15px;
}

.avatar-upload {
    overflow: hidden;

    label {
        display: block;
        float: left;
        clear: left;
        width: 100px;
    }

    input {
        display: block;
        margin-left: 110px;
    }
}

.avater-alert {
    margin-top: 10px;
    margin-bottom: 10px;
}

.avatar-wrapper {
    height: 364px;
    width: 100%;
    margin-top: 15px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, .25);
    background-color: #fcfcfc;
    overflow: hidden;

    img {
        display: block;
        height: auto;
        max-width: 100%;
    }
}

.avatar-preview {
    float: left;
    margin-top: 15px;
    margin-right: 15px;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    background-color: #ffffff;
    overflow: hidden;

    &:hover {
        border-color: #ccccff;
        box-shadow: 0 0 5px rgba(0, 0, 0, .15);
    }

    img {
        width: 100%;
    }
}

.preview-lg {
    height: 184px;
    width: 184px;
    margin-top: 15px;
}

.preview-md {
    height: 100px;
    width: 100px;
}

.preview-sm {
    height: 50px;
    width: 50px;
}

@media (min-width: 992px) {
    .avatar-preview {
        float: none;
    }
}

.cropper-loading {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #ffffff url("#{$general-img-path}/loading.gif") no-repeat center center;
    opacity: .75;
    filter: alpha(opacity=75);
    z-index: 20140628;
}

.avatar-view {
    cursor: pointer;
}
