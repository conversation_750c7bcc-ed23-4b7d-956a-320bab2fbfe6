/***
Page Header
***/

.page-header.navbar {
    width: 100%;
    margin: 0;
    border: 0;
    padding: 0;
    box-shadow: none;
    min-height: $header-height;
    filter: none;
    background-image: none;

    .page-header-inner {
        width: 100%;
    }

    /* Fixed header */
    &.navbar-fixed-top {
        z-index: $zindex-header-fixed;
    }

    &.navbar-static-top {
        z-index: $zindex-header-static;
    }

    /* Header logo */
    .page-logo {
        float: left;
        display: block;
        width: $sidebar-logo-container-width;
        height: $header-height;
        padding-left: 20px;

        .page-sidebar-closed.page-sidebar-closed-hide-logo & {
            padding: 0;
        }

        > .logo-image,
        > a {
            display: inline-block;
            float: left;
        }

        .logo-default {
            margin: $header-logo-margin;
        }

        .logo-mini {
            display: none;
            margin-left: 5px;
        }

        .text-logo {
            padding-left: 20px;
            padding-top: 12px;
        }
    }

    /* Menu Toggler */
    .menu-toggler {
        cursor: pointer;
        @include burger-icon(#ffffff, #ffffff, $menu-toggler-size, 1px, -6px, 6px, -6px);
        @include opacity(0.7);
        display: block;
        @include transition('opacity 0.3s');

        &:hover {
            @include transition('opacity 0.3s');
            @include opacity(1);
        }

        &.sidebar-toggler {
            float: right;
            margin: $header-sidebar-toggler-margin;

            .page-sidebar-closed.page-sidebar-closed-hide-logo & {
                margin-right: ($sidebar-collapsed-width - $menu-toggler-size ) * 0.5;
            }
        }

        &.responsive-toggler {
            display: none;
            float: right;
            margin: $header-responsive-toggler-margin;
        }
    }

    /* Top menu */
    .top-menu {
        margin: 0;
        padding: 0;
        float: right;

        .navbar-nav {
            padding: 0;
            margin-right: 20px;
            display: block;

            > li.dropdown {
                margin: 0;
                padding: 0px 4px;
                height: $header-height;
                display: inline-block;
                vertical-align: top;

                &:last-child {
                    padding-right: 0;
                }

                > .dropdown-toggle {
                    margin: 0;
                    padding: $header-top-menu-general-item-padding;
                    max-height: $header-height;

                    &:last-child {
                        padding-right: 0;
                    }

                    > i {
                        font-size: $header-top-menu-icon-font-size;

                        &.glyphicon {
                            font-size: $header-top-menu-icon-font-size - 1;
                        }
                    }

                    > .badge {
                        font-family: $font-family-primary;
                        position: absolute;
                        top: $header-top-menu-general-item-badge-top;
                        right: 20px;
                        font-weight: 300;
                        padding: 3px 6px;
                    }

                    &:focus {
                        background: none;
                    }
                }

                .dropdown-menu {
                    margin-top: 1px;

                    @include border-radius(4px);

                    &:before {
                        position: absolute;
                        top: -7px;
                        right: 9px;
                        display: inline-block !important;
                        border-right: 7px solid transparent;
                        border-bottom: 7px solid #eeeeee;
                        border-left: 7px solid transparent;
                        border-bottom-color: rgba(0, 0, 0, 0.2);
                        content: '';
                    }

                    &:after {
                        position: absolute;
                        top: -6px;
                        right: 10px;
                        display: inline-block !important;
                        border-right: 6px solid transparent;
                        border-bottom: 6px solid #ffffff;
                        border-left: 6px solid transparent;
                        content: '';
                    }

                    > li {
                        > a {
                            color: #555555;

                        }
                    }
                }
            }

            /* Extended Dropdowns */
            > li.dropdown-extended {

                .dropdown-menu {
                    min-width: 160px;
                    max-width: 275px;
                    width: 275px;
                    z-index: $zindex-header-fixed;

                    > li.external {
                        display: block;
                        overflow: hidden;
                        padding: 15px 15px;
                        letter-spacing: 0.5px;

                        @include border-radius(4px 4px 0 0);

                        > h3 {
                            margin: 0;
                            padding: 0;
                            float: left;
                            font-size: 13px;
                            display: inline-block;
                        }

                        > a {
                            display: inline-block;
                            padding: 0;
                            background: none;
                            clear: inherit;
                            font-size: 13px;
                            font-weight: 300;
                            position: absolute;
                            right: 10px;
                            border: 0;
                            margin-top: -1px;

                            &:hover {
                                text-decoration: none;
                            }
                        }
                    }

                    /* header notifications dropdowns */
                    .dropdown-menu-list {
                        padding-right: 0 !important;
                        padding-left: 0;
                        list-style: none;

                        > li {
                            > a {
                                display: block;
                                clear: both;
                                font-weight: 300;
                                line-height: 20px;
                                white-space: normal;
                                font-size: 13px;
                                padding: 16px 15px 18px;
                                text-shadow: none;

                                &:hover {
                                    @include opacity(1);
                                    text-decoration: none;
                                }
                            }

                            &:first-child a {
                                border-top: none;
                            }
                        }
                    }
                }
            }

            /* Notification */
            > li.dropdown-notification {
                .dropdown-menu {
                    .dropdown-menu-list {
                        > li {
                            a {
                                .details {
                                    overflow: hidden;

                                    .label-icon {
                                        margin-right: 10px;
                                        @include border-radius(50%);

                                        i {
                                            margin-right: 2px;
                                            margin-left: 1px;
                                        }

                                        .badge {
                                            right: 15px;
                                        }
                                    }
                                }

                                .time {
                                    float: right;
                                    max-width: 75px;

                                    font-size: 11px;
                                    font-weight: 400;
                                    @include opacity(0.7);

                                    text-align: right;
                                    padding: 1px 5px;
                                }
                            }
                        }
                    }
                }
            }

            /* Inbox */
            > li.dropdown-inbox {

                > .dropdown-menu {
                    .dropdown-menu-list {
                        > li {
                            .photo {
                                float: left;
                                margin: 0 6px 6px 0;

                                img {
                                    height: 40px;
                                    width: 40px;
                                    @include border-radius(50% !important);
                                }
                            }

                            .subject {
                                display: block;
                                margin-left: 46px;

                                .from {
                                    font-size: 13px;
                                    font-weight: 600;
                                }

                                .time {
                                    font-size: 12px;
                                    font-weight: 400;
                                    @include opacity(0.5);
                                    float: right;
                                }
                            }

                            .message {
                                display: block !important;
                                font-size: 12px;
                                line-height: 1.3;
                                margin-left: 46px;
                            }
                        }
                    }
                }
            }

            /* Tasks */
            > li.dropdown-tasks {
                .dropdown-menu {
                    .dropdown-menu-list {
                        > li {
                            .task {
                                margin-bottom: 5px;

                                .desc {
                                    font-size: 13px;
                                    font-weight: 300;
                                }

                                .percent {
                                    float: right;
                                    font-weight: 600;
                                    display: inline-block;
                                }
                            }

                            .progress {
                                display: block;
                                height: 8px;
                                margin: 8px 0 2px;

                                .progress-bar {
                                    box-shadow: none;
                                }
                            }
                        }
                    }
                }
            }

            /* User */
            > li.dropdown-user {
                .dropdown-toggle {
                    padding: $header-top-menu-user-item-padding;

                    > .username {
                        display: inline-block;
                        font-size: $header-top-menu-user-font-size;
                        font-weight: $header-top-menu-user-font-weight;
                    }

                    > img {
                        float: left;
                        margin-top: -5px;
                        margin-right: 5px;
                        height: 29px;
                        display: inline-block;
                    }

                    > i {
                        display: inline-block;
                        margin: 0;
                        font-size: $header-top-menu-user-font-size;
                    }
                }

                .dropdown-menu {
                    width: 175px;

                    > li {
                        > a {
                            font-size: $header-top-menu-user-dropdown-link-font-size;
                            font-weight: $header-top-menu-user-dropdown-link-font-weight;

                            i {
                                width: 15px;
                                display: inline-block;
                                margin-right: 9px;
                            }

                            .badge {
                                margin-right: 10px;
                            }
                        }
                    }
                }
            }

            /* Language */
            > li.dropdown-language {
                padding-left: 0;
                padding-right: 0;
                margin: 0;

                > .dropdown-toggle {
                    padding: $header-top-menu-language-item-padding;

                    > img {
                        margin-bottom: 2px;
                    }

                    > i {
                        font-size: 14px;
                    }
                }

                > .dropdown-menu {
                    > li {
                        > a {
                            font-size: 13px;

                            > img {
                                margin-bottom: 2px;
                                margin-right: 5px;
                            }
                        }
                    }
                }
            }

            /* Dark version */
            li.dropdown-dark {
                .dropdown-menu {
                    &:before {
                        border-left: none;
                        border-right: none;
                    }

                    .dropdown-menu-list {
                        > li.external {

                            a {
                                background: none !important;
                                border: none !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

/***
Horizontal Menu
***/

.page-header.navbar {

    /* Header container */
    .container {
        position: relative;
    }
}

@media (min-width: $screen-md-min) { /* 992px */

    /* Page header */
    .page-header.navbar {
        /* Header logo */
        .page-logo {
            .page-sidebar-closed.page-sidebar-closed-hide-logo & {
                padding: 0;
            }

            .page-sidebar-closed.page-sidebar-closed-hide-logo & {
                width: $sidebar-collapsed-width;

                .logo-default {
                    display: none;
                }
            }
        }
    }
}

@media (max-width: $screen-sm-max) { /* 991px */

    /* Page header */
    .page-header.navbar {
        position: relative;
        clear: both;

        /* Page logo */
        .page-logo {
            width: auto;
            padding: 0;
            margin-right: 10px;
            margin-left: 0 !important;
            padding-left: 0 !important;

            img {
                margin-left: 4px !important;
            }
        }

        /* Menu Toggler */
        .menu-toggler {
            &.sidebar-toggler {
                display: none !important;
            }

            &.responsive-toggler {
                display: block;
            }
        }

        /* Top Menu */
        .top-menu {
            .navbar-nav {
                display: inline-block;

                > li {
                    float: left;
                }

                .nav li.dropdown i {
                    display: inline-block;
                    position: relative;
                    top: 1px;
                    right: 0;
                }

                .open .dropdown-menu {
                    position: absolute;
                }
            }
        }
    }

    /* Fixed header for mobile */
    .page-header-fixed.page-header-fixed-mobile {
        .navbar-fixed-top {
            position: fixed;
        }
    }
}

@media (max-width: $screen-xs-max) { /* 767px */

    /* Page header */
    .page-header.navbar {
        .page-header-inner {
            width: 100%;
        }

        /* Header logo */
        .page-logo {
            width: auto;
            margin-left: 15px !important;
        }

        .menu-toggler {
            margin-right: 15px !important;
        }

        /* Top navigation menu*/
        .top-menu {
            width: 100%;

            .float-end {
                float: none !important;
            }

            .navbar-nav {

                .dropdown-menu {
                    &:before {
                        width: 10px;
                    }
                }

                > li.dropdown-extended {
                    > .dropdown-menu {
                        max-width: 255px;
                        width: 255px;
                    }
                }

                > li.dropdown-notification {
                    .dropdown-menu {
                        margin-right: -190px;

                        &:after,
                        &:before {
                            margin-right: 190px;
                        }
                    }
                }

                > li.dropdown-inbox {
                    .dropdown-menu {
                        margin-right: -150px;

                        &:after,
                        &:before {
                            margin-right: 150px;
                        }
                    }
                }

                > li.dropdown-tasks {
                    .dropdown-menu {
                        margin-right: -110px;

                        &:after,
                        &:before {
                            margin-right: 110px;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 580px) {

    .top-menu {
        .navbar-nav {
            > li.dropdown-user {
                .dropdown-toggle {
                    .username.username-hide-on-mobile {
                        display: none;
                    }
                }
            }

            > li.dropdown-language {
                .dropdown-toggle {
                    .langname {
                        display: none;
                    }
                }
            }
        }
    }
}

@media (max-width: $screen-xs-min) { /* 480px */

    /* Fixed header for mobile */
    .page-header-fixed.page-header-fixed-mobile {
        .page-header.navbar {
            height: $header-height-xs;
        }
    }

    /* Top menu */
    .top-menu {
        display: block;
        clear: both;
        float: none;
        margin: 0 -10px;

        .navbar-nav {
            margin-right: 0;

            > li.dropdown {
                .dropdown-toggle {
                    padding: $header-top-menu-general-item-padding-on-mobile;
                }
            }

            > li.dropdown-language {
                .dropdown-toggle {
                    padding: $header-top-menu-language-item-padding-on-mobile;
                }
            }

            > li.dropdown-user {
                .dropdown-toggle {
                    padding: $header-top-menu-user-item-padding-on-mobile;
                }
            }
        }
    }
}

/***
Pace - Page Progress
***/

.pace {

    .pace-progress {
        z-index: $zindex-header-fixed + 10;
        top: $header-height;
        height: 2px;
        box-shadow: none;
    }

    .pace-progress-inner {
        box-shadow: none;
    }

    .pace-inactive {
        display: none;
    }

    .pace-activity {
        top: $header-height + 4px;
        z-index: $zindex-header-fixed + 10;
        right: 20px;
        border-radius: 10px !important;
    }
}

@media (max-width: $screen-xs-min) { /* 480px */

    .page-header-fixed .pace {
        .pace-progress {
            top: ($header-height * 2);
        }

        .pace-activity {
            top: ($header-height * 2) + 4px;
        }
    }
}
