<?php

return [
    'name' => 'Bulk Import',
    'loading_text' => 'Importing',
    'imported_successfully' => 'Imported successfully.',
    'please_choose_the_file_mime' => 'Please choose the file mime :types',
    'please_choose_the_file' => 'Please choose the file',
    'start_import' => 'Start Import',
    'note' => 'Note!',
    'warning_before_importing' => 'If this is your first time using this feature, please read the documentation or contact the Administrator for access to the correct functionality!',
    'results' => 'Result: :success successes, :failed failures',
    'failures' => 'Failures',
    'tables' => [
        'import' => 'Import',
    ],
    'template' => 'Template',
    'rules' => 'Rules',
    'choose_file_with_mime' => 'Choose file with mime: (:types)',
    'choose_file' => 'Choose file',
    'menu' => 'Import products',
    'download-csv-file' => 'Download CSV template',
    'download-excel-file' => 'Download Excel template',
    'downloading' => 'Downloading...',
    'export' => [
        'template' => [
            'input_error' => 'Input error',
            'number_not_allowed' => 'Number is not allowed!',
            'allowed_input' => 'Allowed input',
            'prompt_decimal' => 'Only numbers or decimals are accepted and greater than or equal to :min.',
            'prompt_whole_number' => 'Only numbers are accepted and greater than or equal to :min.',
            'prompt_list' => 'Please pick a value from the drop-down list.',
            'pick_from_list' => 'Pick from list',
            'value_not_in_list' => 'Value is not in list.',
        ],
    ],
    'import_failed_description' => 'Import failed, please check the errors below!',
    'column' => 'Column',
    'row' => 'Row',
    'attribute' => 'Attribute',
    'errors' => 'Errors',
    'import_types' => [
        'name' => 'Import type',
        'all' => 'All',
        'products' => 'Products',
        'variations' => 'Variations',
    ],
];
