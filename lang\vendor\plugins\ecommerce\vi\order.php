<?php

return [
    'statuses' => [
        'pending' => 'Chưa xử lý',
        'processing' => 'Đang xử lý',
        'completed' => 'Hoàn thành',
        'canceled' => 'Bị huỷ',
    ],
    'name' => 'Đơn hàng',
    'incomplete_order' => 'Đơn hàng chưa hoàn tất',
    'order_id' => 'Mã đơn hàng',
    'customer_label' => 'Khách hàng',
    'amount' => 'Tổng cộng',
    'shipping_amount' => 'Phí vận chuyển',
    'payment_method' => 'Phương thức thanh toán',
    'payment_status_label' => 'Trạng thái thanh toán',
    'manage_orders' => 'Quản lý đơn hàng',
    'order_intro_description' => 'Một khi cửa hàng của bạn có đơn đặt hàng, đây sẽ là nơi bạn xử lý và theo dõi những đơn đặt hàng đó.',
    'create_new_order' => 'Tạo đơn hàng',
    'manage_incomplete_orders' => 'Quản lý đơn hàng chưa hoàn tất',
    'incomplete_orders_intro_description' => 'Đơn hàng chưa hoàn tất là đơn hàng được tạo khi khách hàng thêm sản phẩm vào giỏ hàng, tiến hành điền thông tin mua hàng nhưng không hoàn tất quá trình thanh toán.',
    'cannot_send_order_recover_to_mail' => 'Không tìm thấy email nên không thể gửi email khôi phục cho khách hàng.',
    'menu' => 'Đơn hàng',
    'order_information' => 'Thông tin đơn hàng',
    'create' => 'Create an order',
    'cancel_error' => 'The order is delivering or completed',
    'cancel_success' => 'You do cancel the order successful',
    'product_id' => 'Product ID',
    'tax_amount' => 'Tax Amount',
    'invoice_for_order' => 'Invoice for order',
    'created' => 'Created',
    'invoice' => 'Invoice',
    'return' => 'Return',
    'total_refund_amount' => 'Total refund amount',
    'total_amount_can_be_refunded' => 'Total amount can be refunded',
    'refund_reason' => 'Refund reason (optional)',
    'products' => 'product(s)',
    'default' => 'Default',
    'system' => 'System',
    'new_order_from' => 'New order :order_id from :customer',
    'confirmation_email_was_sent_to_customer' => 'The email confirmation was sent to customer',
    'address_name_required' => 'The name field is required.',
    'address_phone_required' => 'The phone field is required.',
    'address_email_required' => 'The email field is required.',
    'address_email_unique' => 'The customer with that email is existed, please choose other email or login with this email!',
    'address_state_required' => 'The state field is required.',
    'address_city_required' => 'The city field is required.',
    'address_country_required' => 'The country field is required.',
    'address_address_required' => 'The address field is required.',
    'create_order_from_payment_page' => 'Order was created from checkout page',
    'order_was_verified_by' => 'Order was verified by %user_name%',
    'new_order' => 'New order :order_id',
    'payment_was_confirmed_by' => 'Payment was confirmed (amount :money) by %user_name%',
    'edit_order' => 'Edit order :code',
    'confirm_order_success' => 'Confirm order successfully!',
    'error_when_sending_email' => 'There is an error when sending email',
    'sent_confirmation_email_success' => 'Sent confirmation email successfully!',
    'order_was_sent_to_shipping_team' => 'Order was sent to shipping team',
    'by_username' => 'by %user_name%',
    'shipping_was_created_from' => 'Shipping was created from order %order_id%',
    'shipping_was_canceled_success' => 'Shipping was cancelled successfully!',
    'shipping_was_canceled_by' => 'Shipping was cancelled by %user_name%',
    'update_shipping_address_success' => 'Update shipping address successfully!',
    'order_was_canceled_by' => 'Order was cancelled by %user_name%',
    'confirm_payment_success' => 'Confirm payment successfully!',
    'refund_amount_invalid' => 'Refund amount must be lower or equal :price',
    'number_of_products_invalid' => 'Number of products refund is not valid!',
    'cannot_found_payment_for_this_order' => 'Cannot found payment for this order!',
    'refund_success_with_price' => 'Refund success :price',
    'refund_success' => 'Refund successfully!',
    'order_is_not_existed' => 'Order is not existed!',
    'reorder' => 'Reorder',
    'sent_email_incomplete_order_success' => 'Sent email to remind about incomplete order successfully!',
    'applied_coupon_success' => 'Applied coupon ":code" successfully!',
    'new_order_notice' => 'You have <span class="bold">:count</span> New Order(s)',
    'view_all' => 'View all',
    'cancel_order' => 'Cancel order',
    'order_canceled' => 'Order canceled',
    'order_was_canceled_at' => 'Order was canceled at',
    'completed' => 'Completed',
    'uncompleted' => 'Uncompleted',
    'sku' => 'SKU',
    'warehouse' => 'Warehouse',
    'sub_amount' => 'Sub amount',
    'coupon_code' => 'Coupon code: ":code"',
    'shipping_fee' => 'Shipping fee',
    'tax' => 'Tax',
    'refunded_amount' => 'Refunded amount',
    'amount_received' => 'The amount actually received',
    'download_invoice' => 'Download invoice',
    'add_note' => 'Add note...',
    'order_was_confirmed' => 'Order was confirmed',
    'confirm_order' => 'Confirm order',
    'confirm' => 'Confirm',
    'order_was_canceled' => 'Order was canceled',
    'pending_payment' => 'Pending payment',
    'payment_was_accepted' => 'Payment :money was accepted',
    'payment_was_refunded' => 'Payment was refunded',
    'confirm_payment' => 'Confirm payment',
    'refund' => 'Refund',
    'all_products_are_not_delivered' => 'All products are not delivered',
    'delivery' => 'Delivery',
    'history' => 'History',
    'order_number' => 'Order number',
    'from' => 'from',
    'status' => 'Status',
    'successfully' => 'Successfully',
    'transaction_type' => 'Transaction\'s type',
    'staff' => 'Staff',
    'refund_date' => 'Refund date',
    'n_a' => 'N\\A',
    'payment_date' => 'Payment date',
    'payment_gateway' => 'Payment gateway',
    'transaction_amount' => 'Transaction amount',
    'resend' => 'Resend',
    'default_store' => 'Default store',
    'update_address' => 'Update address',
    'have_an_account_already' => 'Have an account already',
    'dont_have_an_account_yet' => 'Don\'t have an account yet',
    'mark_payment_as_confirmed' => 'Mark <span>:method</span> as confirmed',
    'resend_order_confirmation' => 'Resend order confirmation',
    'resend_order_confirmation_description' => 'Confirmation email will be sent to <strong>:email</strong>?',
    'send' => 'Send',
    'update' => 'Update',
    'cancel_shipping_confirmation' => 'Cancel shipping confirmation?',
    'cancel_shipping_confirmation_description' => 'Cancel shipping confirmation?',
    'cancel_order_confirmation' => 'Cancel order confirmation?',
    'cancel_order_confirmation_description' => 'Are you sure you want to cancel this order? This action cannot rollback',
    'confirm_payment_confirmation_description' => 'Processed by <strong>:method</strong>. Did you receive payment outside the system? This payment won\'t be saved into system and cannot be refunded',
    'save_note' => 'Save note',
    'order_note' => 'Order note',
    'order_note_placeholder' => 'Note about order, ex: time or shipping instruction.',
    'order_amount' => 'Order amount',
    'additional_information' => 'Additional information',
    'notice_about_incomplete_order' => 'Notice about incomplete order',
    'notice_about_incomplete_order_description' => 'Remind email about uncompleted order will be send to <strong>:email</strong>?',
    'incomplete_order_description_1' => 'An incomplete order is when a potential customer places items in their shopping cart, and goes all the way through to the payment page, but then doesn\'t complete the transaction.',
    'incomplete_order_description_2' => 'If you have contacted customers and they want to continue buying, you can help them complete their order by following the link:',
    'send_an_email_to_recover_this_order' => 'Send an email to customer to recover this order',
    'see_maps' => 'See maps',
    'one_or_more_products_dont_have_enough_quantity' => 'One or more products don\'t have enough quantity!',
    'payment_info' => 'Payment Info',
    'payment_method_refund_automatic' => 'Your customer will be refunded using :method automatically.',
    'order' => 'Order',
    'create_a_new_product' => 'Create a new product',
    'out_of_stock' => 'Out of stock',
    'products_available' => 'product(s) available',
    'no_products_found' => 'No products found!',
    'note' => 'Note',
    'note_for_order' => 'Note for order...',
    'add_discount' => 'Add discount',
    'discount' => 'Discount',
    'add_shipping_fee' => 'Add shipping fee',
    'shipping' => 'Shipping',
    'total_amount' => 'Total amount',
    'confirm_payment_and_create_order' => 'Confirm payment and create order',
    'paid' => 'Paid',
    'pay_later' => 'Pay later',
    'customer_information' => 'Customer information',
    'create_new_customer' => 'Create new customer',
    'no_customer_found' => 'No customer found!',
    'customer' => 'Customer',
    'orders' => 'order(s)',
    'shipping_address' => 'Shipping Address',
    'see_on_maps' => 'See on maps',
    'price' => 'Price',
    'sku_optional' => 'SKU (optional)',
    'with_storehouse_management' => 'With storehouse management?',
    'quantity' => 'Quantity',
    'allow_customer_checkout_when_this_product_out_of_stock' => 'Allow customer checkout when this product out of stock?',
    'address' => 'Address',
    'phone' => 'Phone',
    'country' => 'Country',
    'state' => 'State',
    'city' => 'City',
    'zip_code' => 'Zip code',
    'discount_based_on' => 'Discount based on',
    'or_coupon_code' => 'Or coupon code',
    'description' => 'Description',
    'how_to_select_configured_shipping' => 'How to select configured shipping?',
    'please_add_customer_information_with_the_complete_shipping_address_to_see_the_configured_shipping_rates' => 'Please add customer information with the complete shipping address to see the configured shipping rates',
    'free_shipping' => 'Free shipping',
    'custom' => 'Custom',
    'email' => 'Email',
    'create_order' => 'Create order',
    'close' => 'Close',
    'confirm_payment_is_paid_for_this_order' => 'Confirm payment is paid for this order',
    'payment_status_of_the_order_is_paid_once_the_order_has_been_created_you_cannot_change_the_payment_method_or_status' => 'Payment status of the order is Paid. Once the order has been created, you cannot change the payment method or status',
    'select_payment_method' => 'Select payment method',
    'cash_on_delivery_cod' => 'Cash on delivery (COD)',
    'bank_transfer' => 'Bank transfer',
    'paid_amount' => 'Paid amount',
    'confirm_that_payment_for_this_order_will_be_paid_later' => 'Confirm that payment for this order will be paid later',
    'payment_status_of_the_order_is_pending_once_the_order_has_been_created_you_cannot_change_the_payment_method_or_status' => 'Payment status of the order is Pending. Once the order has been created, you cannot change the payment method or status',
    'pending_amount' => 'Pending amount',
    'update_email' => 'Update email',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'create_a_new_order' => 'Create a new order',
    'search_or_create_new_product' => 'Search or create a new product',
    'search_or_create_new_customer' => 'Search or create a new customer',
    'discount_description' => 'Discount description',
    'cant_select_out_of_stock_product' => 'Cannot select out of stock product!',
];
