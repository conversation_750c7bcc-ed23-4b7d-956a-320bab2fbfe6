<?php

return [
    'customer_new_order_title' => 'Order confirmation',
    'customer_new_order_description' => 'Send email confirmation to customer when an order placed',

    'order_cancellation_title' => 'Order cancellation',
    'order_cancellation_description' => 'Send to custom when they cancelled order',

    'delivery_confirmation_title' => 'Delivering confirmation',
    'delivery_confirmation_description' => 'Send to customer when order is delivering',

    'admin_new_order_title' => 'Notice about new order',
    'admin_new_order_description' => 'Send to administrators when an order placed',

    'order_confirmation_title' => 'Order confirmation',
    'order_confirmation_description' => 'Send to customer when they order was confirmed by admins',

    'payment_confirmation_title' => 'Payment confirmation',
    'payment_confirmation_description' => 'Send to customer when their payment was confirmed',

    'order_recover_title' => 'Incomplete order',
    'order_recover_description' => 'Send to custom to remind them about incomplete orders',
    'view_order' => 'View order',
    'link_go_to_our_shop' => 'or <a href=":link">Go to our shop</a>',
    'order_number' => 'Order number: <strong>:order_id</strong>',
    'order_information' => 'Order information:',

    'order_return_request_title' => 'Order return request',
    'order_return_request_description' => 'Send to customer when they return order',

];
