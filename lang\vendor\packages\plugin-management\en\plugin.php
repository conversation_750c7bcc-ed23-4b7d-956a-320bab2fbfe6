<?php

return [
    'enabled' => 'Enabled',
    'deactivated' => 'Deactivated',
    'activated' => 'Activated',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'author' => 'By',
    'update_plugin_status_success' => 'Update plugin successfully',
    'plugins' => 'Plugins',
    'missing_required_plugins' => 'Please activate plugin(s): :plugins before activate this plugin!',
    'remove' => 'Remove',
    'remove_plugin_success' => 'Remove plugin successfully!',
    'remove_plugin' => 'Remove plugin',
    'remove_plugin_confirm_message' => 'Do you really want to remove this plugin?',
    'remove_plugin_confirm_yes' => 'Yes, remove it!',
    'total_plugins' => 'Total plugins',
    'invalid_plugin' => 'This plugin is not a valid plugin, please check it again!',
    'version' => 'Version',
    'invalid_json' => 'Invalid plugin.json!',
    'activate_success' => 'Activate plugin successfully!',
    'activated_already' => 'This plugin is activated already!',
    'plugin_not_exist' => 'This plugin is not exists.',
    'missing_json_file' => 'Missing file plugin.json!',
    'plugin_invalid' => 'Plugin is valid!',
    'published_assets_success' => 'Publish assets for plugin :name successfully!',
    'plugin_removed' => 'Plugin has been removed!',
    'deactivated_success' => 'Deactivate plugin successfully!',
    'deactivated_already' => 'This plugin is deactivated already!',
    'folder_is_not_writeable' => 'Cannot write files! Folder :name is not writable. Please chmod to make it writable!',
    'plugin_is_not_ready' => 'Plugin :name is not ready to use',
    'plugins_installed' => 'Installed Plugins',
    'plugins_add_new' => 'Add new',
    'update' => 'Update',
];
