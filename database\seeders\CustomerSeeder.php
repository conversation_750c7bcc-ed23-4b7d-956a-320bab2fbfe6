<?php

namespace Database\Seeders;

use <PERSON><PERSON>ble\Base\Supports\BaseSeeder;
use <PERSON><PERSON>ble\Ecommerce\Models\Address;
use <PERSON><PERSON>ble\Ecommerce\Models\Customer;
use Faker\Factory;

class CustomerSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->uploadFiles('customers');

        $faker = Factory::create();

        Customer::truncate();
        Address::truncate();

        $customer = Customer::create([
            'name' => '<PERSON> Smith',
            'email' => '<EMAIL>',
            'password' => bcrypt('12345678'),
            'phone' => $faker->e164PhoneNumber,
            'avatar' => 'customers/1.jpg',
            'dob' => now()->subYears(rand(20, 50))->subDays(rand(1, 30)),
        ]);

        $customer->confirmed_at = now();
        $customer->save();

        Address::create([
            'name' => $customer->name,
            'phone' => $faker->e164PhoneNumber,
            'email' => $customer->email,
            'country' => $faker->countryCode,
            'state' => $faker->state,
            'city' => $faker->city,
            'address' => $faker->streetAddress,
            'zip_code' => $faker->postcode,
            'customer_id' => $customer->id,
            'is_default' => true,
        ]);

        Address::create([
            'name' => $customer->name,
            'phone' => $faker->e164PhoneNumber,
            'email' => $customer->email,
            'country' => $faker->countryCode,
            'state' => $faker->state,
            'city' => $faker->city,
            'address' => $faker->streetAddress,
            'zip_code' => $faker->postcode,
            'customer_id' => $customer->id,
            'is_default' => false,
        ]);

        for ($i = 0; $i < 10; $i++) {
            $customer = Customer::create([
                'name' => $faker->name,
                'email' => $faker->unique()->safeEmail,
                'password' => bcrypt('12345678'),
                'phone' => $faker->e164PhoneNumber,
                'avatar' => 'customers/' . ($i + 1) . '.jpg',
                'dob' => now()->subYears(rand(20, 50))->subDays(rand(1, 30)),
            ]);

            $customer->confirmed_at = now();
            $customer->save();

            Address::create([
                'name' => $customer->name,
                'phone' => $faker->e164PhoneNumber,
                'email' => $customer->email,
                'country' => $faker->countryCode,
                'state' => $faker->state,
                'city' => $faker->city,
                'address' => $faker->streetAddress,
                'zip_code' => $faker->postcode,
                'customer_id' => $customer->id,
                'is_default' => true,
            ]);
        }
    }
}
