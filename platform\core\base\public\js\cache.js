(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e,r){for(var n=0;n<r.length;n++){var o=r[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(i=o.key,a=void 0,a=function(e,r){if("object"!==t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,r||"default");if("object"!==t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(i,"string"),"symbol"===t(a)?a:String(a)),o)}var i,a}var r=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var r,n,o;return r=t,(n=[{key:"init",value:function(){$(document).on("click",".btn-clear-cache",(function(t){t.preventDefault();var e=$(t.currentTarget);e.addClass("button-loading"),$.ajax({url:e.data("url"),type:"POST",data:{type:e.data("type")},success:function(t){e.removeClass("button-loading"),t.error?Botble.showError(t.message):Botble.showSuccess(t.message)},error:function(t){e.removeClass("button-loading"),Botble.handleError(t)}})}))}}])&&e(r.prototype,n),o&&e(r,o),Object.defineProperty(r,"prototype",{writable:!1}),t}();$(document).ready((function(){(new r).init()}))})();