// OVERLAY

.mt-element-overlay {

    .mt-overlay-1 {
        width: 100%;
        height: 100%;
        float: left;
        overflow: hidden;
        position: relative;
        text-align: center;
        cursor: default;

        img {
            display: block;
            position: relative;
            -webkit-transition: all .4s linear;
            transition: all .4s linear;
            width: 100%;
            height: auto;
        }

        h2 {
            text-transform: uppercase;
            color: #ffffff;
            text-align: center;
            position: relative;
            font-size: 17px;
            background: rgba(0, 0, 0, 0.6);
            -webkit-transform: translatey(-100px) translateZ(0);
            -ms-transform: translatey(-100px) translateZ(0);
            transform: translatey(-100px) translateZ(0);
            -webkit-transition: all .2s ease-in-out;
            transition: all .2s ease-in-out;
            padding: 10px;
        }

        .mt-info {
            text-decoration: none;
            display: inline-block;
            text-transform: uppercase;
            color: #ffffff;
            background-color: transparent;
            opacity: 0;
            filter: alpha(opacity=0);
            -webkit-transition: all .2s ease-in-out;
            transition: all .2s ease-in-out;
            padding: 0;
            margin: auto;
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            transform: translateY(-50%) translateZ(0);
            -webkit-transform: translateY(-50%) translateZ(0);
            -ms-transform: translateY(-50%) translateZ(0);

            > li {
                list-style: none;
                display: inline-block;
                margin: 0 3px;

                &:hover {
                    -webkit-transition: all .2s ease-in-out;
                    transition: all .2s ease-in-out;
                    cursor: pointer;
                }

            }
        }

        &:hover {

            .mt-overlay {
                opacity: 1;
                filter: alpha(opacity=100);
                -webkit-transform: translateZ(0);
                -ms-transform: translateZ(0);
                transform: translateZ(0);
            }

            img {
                -ms-transform: scale(1.2) translateZ(0);
                -webkit-transform: scale(1.2) translateZ(0);
                transform: scale(1.2) translateZ(0);
            }

            .mt-info {
                opacity: 1;
                filter: alpha(opacity=100);
                -webkit-transition-delay: .2s;
                transition-delay: .2s;
            }
        }

        .mt-overlay {
            width: 100%;
            height: 100%;
            position: absolute;
            overflow: hidden;
            top: 0;
            left: 0;
            opacity: 0;
            background-color: rgba(0, 0, 0, 0.7);
            -webkit-transition: all .4s ease-in-out;
            transition: all .4s ease-in-out;
        }

        &.mt-scroll-up {

            &:hover {

                .mt-overlay {
                    bottom: 0;
                }

            }

            .mt-overlay {
                bottom: -100%;
                top: auto;
            }

        }

        &.mt-scroll-down {

            &:hover {

                .mt-overlay {
                    top: 0;
                }

            }

            .mt-overlay {
                top: -100%;
            }

        }

        &.mt-scroll-left {

            &:hover {

                .mt-overlay {
                    right: 0;
                }

            }

            .mt-overlay {
                right: -100%;
                left: auto;
            }

        }

        &.mt-scroll-right {

            &:hover {

                .mt-overlay {
                    left: 0;
                }

            }

            .mt-overlay {
                left: -100%;
            }

        }

    }

}
