(()=>{function e(r){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(r)}function r(r,o){for(var t=0;t<o.length;t++){var n=o[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,(s=n.key,i=void 0,i=function(r,o){if("object"!==e(r)||null===r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var n=t.call(r,o||"default");if("object"!==e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(r)}(s,"string"),"symbol"===e(i)?i:String(i)),n)}var s,i}var o=function(){function e(){!function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}(this,e)}var o,t,n;return o=e,(t=[{key:"handleLogin",value:function(){$(".login-form").validate({errorElement:"span",errorClass:"help-block",focusInvalid:!1,rules:{username:{required:!0},password:{required:!0},remember:{required:!1}},messages:{username:{required:"Username is required."},password:{required:"Password is required."}},invalidHandler:function(){$(".alert-danger",$(".login-form")).show()},highlight:function(e){$(e).closest(".form-group").addClass("has-error")},success:function(e){e.closest(".form-group").removeClass("has-error"),e.remove()},errorPlacement:function(e,r){e.insertAfter(r.closest(".form-control"))},submitHandler:function(e){e.submit()}}),$(".login-form input").keypress((function(e){if(13===e.which){var r=$(".login-form");return r.validate().form()&&r.submit(),!1}}))}},{key:"handleForgetPassword",value:function(){$(".forget-form").validate({errorElement:"span",errorClass:"help-block",focusInvalid:!1,ignore:"",rules:{email:{required:!0,email:!0}},messages:{email:{required:"Email is required."}},invalidHandler:function(){$(".alert-danger",$(".forget-form")).show()},highlight:function(e){$(e).closest(".form-group").addClass("has-error")},success:function(e){e.closest(".form-group").removeClass("has-error"),e.remove()},errorPlacement:function(e,r){e.insertAfter(r.closest(".form-control"))},submitHandler:function(e){e.submit()}}),$(".forget-form input").keypress((function(e){if(13===e.which)return $(".forget-form").validate().form()&&$(".forget-form").submit(),!1}))}},{key:"init",value:function(){this.handleLogin(),this.handleForgetPassword()}}])&&r(o.prototype,t),n&&r(o,n),Object.defineProperty(o,"prototype",{writable:!1}),e}();$(document).ready((function(){(new o).init();var e=document.querySelector('[name="username"]'),r=document.querySelector('[name="email"]'),t=document.querySelector('[name="password"]'),n=document.querySelector('[name="password_confirmation"]');e&&(e.focus(),document.getElementById("emailGroup").classList.add("focused"),e.addEventListener("focusin",(function(){document.getElementById("emailGroup").classList.add("focused")})),e.addEventListener("focusout",(function(){document.getElementById("emailGroup").classList.remove("focused")}))),r&&(r.focus(),document.getElementById("emailGroup").classList.add("focused"),r.addEventListener("focusin",(function(){document.getElementById("emailGroup").classList.add("focused")})),r.addEventListener("focusout",(function(){document.getElementById("emailGroup").classList.remove("focused")}))),t&&(t.addEventListener("focusin",(function(){document.getElementById("passwordGroup").classList.add("focused")})),t.addEventListener("focusout",(function(){document.getElementById("passwordGroup").classList.remove("focused")}))),n&&(n.addEventListener("focusin",(function(){document.getElementById("passwordConfirmationGroup").classList.add("focused")})),n.addEventListener("focusout",(function(){document.getElementById("passwordConfirmationGroup").classList.remove("focused")})))}))})();