// Theme Colors Settings
// Main settings
$theme-name: 'light';
$brand-main-color: #3fd5c0 !default;
$brand-main-font-color: #ffffff !default;

// Body and header
$bg-color: #ffffff !default;
$header-bg-color: #126ba0 !default;
$header-toggler-color: lighten($header-bg-color, 50%) !default;
$header-toggler-bg-color: lighten($header-bg-color, 3%) !default;

// Top menu
$header-top-menu-bg-hover-color: lighten($header-bg-color, 10%) !default;
$header-top-menu-bg-hover-color-on-dropdown-open: lighten($header-bg-color, 10%) !default;
$header-top-menu-badge-bg-color: $brand-main-color;
$header-top-menu-badge-font-color: #ffffff;
$header-top-menu-icon-font-color: lighten(#9fa7b1, 10%) !default;

// Top menu user bar
$header-top-menu-user-font-color: lighten($header-bg-color, 50%) !default;
$header-top-menu-user-bg-color: darken($header-bg-color, 4%) !default;

//begin: Top Menu Extended Dropdowns
$header-top-menu-extended-dropdown-item-bg-color-on-hover: #f8f9fa;
$header-top-menu-extended-dropdown-item-border-color: #eff2f6;
$header-top-menu-extended-dropdown-item-font-color: #888888;

$header-top-menu-extended-dropdown-header-bg-color: darken(#f7f8fa, 4%);
$header-top-menu-extended-dropdown-border-color: darken($header-top-menu-extended-dropdown-header-bg-color, 1%);
$header-top-menu-extended-dropdown-header-font-color: darken(#6f949c, 5%);

$header-top-menu-inbox-dropdown-from-font-color: #5b9bd1;

$header-top-menu-task-dropdown-progress-bg-color: #dfe2e9;

$header-top-menu-notification-time-bg-color: lighten(#eeeeee, 1%);

// Top Menu Notifications
$header-top-menu-dropdown-dark-bg-color: darken($header-bg-color, 1%);
$header-top-menu-dropdown-dark-header-bg-color: darken($header-bg-color, 5%);
$header-top-menu-dropdown-dark-header-font-color: lighten($header-top-menu-dropdown-dark-header-bg-color, 40%);

$header-top-menu-dropdown-dark-item-font-color: lighten($header-top-menu-dropdown-dark-header-bg-color, 40%);
$header-top-menu-dropdown-dark-item-border-color: lighten($header-top-menu-dropdown-dark-bg-color, 3%);
$header-top-menu-dropdown-dark-item-icon-color: lighten($header-top-menu-dropdown-dark-header-bg-color, 30%);
$header-top-menu-dropdown-dark-item-bg-color-on-hover: lighten($header-top-menu-dropdown-dark-bg-color, 2%);

$header-top-menu-dropdown-dark-default-menu-divider: $header-top-menu-dropdown-dark-item-border-color;
$header-top-menu-dropdown-dark-notification-time-bg-color: darken($header-top-menu-dropdown-dark-bg-color, 2%);
//end: Top Menu Extended Dropdowns

// Sidebar menu
$sidebar-menu-deliver-border-color: #f0f5f7 !default;

$sidebar-menu-link-font-color: #555555 !default;
$sidebar-menu-link-font-color-on-hover: #ffffff !default;
$sidebar-menu-link-font-color-on-active: #ffffff !default;

$sidebar-menu-link-icon-font-color: #c3c3c3 !default;
$sidebar-menu-link-icon-font-color-on-active: $sidebar-menu-link-font-color-on-active !default;
$sidebar-menu-link-icon-font-color-on-hover: lighten($sidebar-menu-link-font-color, 35%) !default;

$sidebar-menu-link-bg-color-on-hover: #126ba0 !default;
$sidebar-menu-link-bg-color-on-active: #126ba0;

$sidebar-menu-arrow-color: $sidebar-menu-link-icon-font-color !default;
$sidebar-menu-arrow-color-on-active: $sidebar-menu-link-icon-font-color-on-active !default;
$sidebar-menu-arrow-color-on-hover: $sidebar-menu-link-icon-font-color-on-hover !default;

$sidebar-menu-sub-menu-box-shadow-color: $sidebar-menu-link-bg-color-on-hover !default;
$sidebar-menu-sub-menu-link-font-color: $sidebar-menu-link-font-color !default;
$sidebar-menu-sub-menu-link-icon-font-color: $sidebar-menu-arrow-color !default;
$sidebar-menu-sub-menu-link-bg-color-on-hover: #f1f1f1 !default;
$sidebar-menu-sub-menu-link-icon-font-color-on-hover: $sidebar-menu-link-icon-font-color-on-hover !default;

$sidebar-menu-hover-sub-menu-bg-color: darken($sidebar-menu-link-bg-color-on-hover, 3%) !default;

// Sidebar for mobile
$mobile-sidebar-menu-bg-color: darken($bg-color, 1%) !default;
$mobile-sidebar-menu-link-bg-color-on-hover: darken($bg-color, 15%) !default;

// Light sidebar menu
$light-sidebar-menu-link-border-color: $brand-main-color !default;
$light-sidebar-menu-link-bg-color-on-hover: darken($bg-color, 4%) !default;
$light-sidebar-menu-link-bg-color-on-active: darken($bg-color, 7%) !default;
$light-sidebar-menu-link-font-color-on-active: #666666 !default;
$light-sidebar-menu-link-icon-color-on-active: #999999 !default;
$light-sidebar-menu-link-arrow-color-on-active: #999999 !default;

$light-sidebar-menu-sub-menu-bg-color: lighten($bg-color, 2%) !default;
$light-sidebar-menu-sub-menu-link-bg-color-on-hover: darken($bg-color, 7%) !default;

$mobile-light-sidebar-menu-sub-menu-bg-color: $mobile-sidebar-menu-bg-color !default;
$mobile-light-sidebar-menu-link-bg-color-on-hover: lighten($mobile-sidebar-menu-bg-color, 6%) !default;
$mobile-light-sidebar-menu-link-bg-color-on-active: lighten($mobile-sidebar-menu-bg-color, 2%) !default;
$mobile-light-sidebar-menu-sub-menu-link-bg-color-on-hover: lighten($mobile-sidebar-menu-bg-color, 6%) !default;

// Footer
$footer-default-font-color: #333333 !default;
$footer-default-go-top-bg-color: #9ca6ad !default;
$footer-default-go-top-icon-font-color: #dddddd !default;
$footer-fixed-bg-color: lighten($header-bg-color, 40%) !default;

@import '../base';

/* Page sidebar */

select:not([multiple]) {
    border-radius: 3px;
}

.page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover,
.page-sidebar {

    .page-sidebar-menu {

        /* Adjust "selected" arrow */
        > li {

            &.active.open,
            &.active {
                > a {

                    > .selected {
                        top: 0;
                        right: -8px;
                        border-top: 20px double transparent;
                        border-bottom: 20px double transparent;
                        border-right: 0;
                        border-left: 8px solid $brand-main-color;

                        .page-sidebar-reversed & {
                            right: auto;
                            left: -8px;
                            border-left: 0;
                            border-right: 8px solid $brand-main-color;
                        }

                        .page-container-bg-solid & {
                            border-color: transparent transparent transparent $brand-main-color;
                        }

                        .page-container-bg-solid.page-sidebar-reversed & {
                            border-color: transparent $brand-main-color transparent transparent;
                        }
                    }
                }
            }
        }

        /* Change sidebar link font weight */
        li {
            > a {
                font-weight: 400;

                > i {
                    font-weight: 900;
                }
            }

            .page-sidebar-closed &:hover {
                .sub-menu {
                    background-color: #f9f9f9;
                }
            }

            .sub-menu {
                li {
                    > a {
                        font-weight: normal;

                        > i {
                            font-weight: 900;
                        }
                    }
                }
            }
        }
    }

    .page-sidebar-menu.page-sidebar-menu-light {
        > li {
            > a {
                .selected {
                    display: none;
                }
            }
        }
    }
}

.page-container-bg-solid {
    .page-content {
        border-left: 1px solid $sidebar-menu-deliver-border-color;
        border-bottom: 1px solid $sidebar-menu-deliver-border-color;

        .page-sidebar-reversed & {
            border-right: 1px solid $sidebar-menu-deliver-border-color;
        }

        background-color: #f1f1f1;

        .meta-boxes {
            border: 1px solid #dedede;
            border-radius: 5px;

            .widget-title {
                border: 1px solid #fbfbfb;
                border-radius: 5px 5px 0 0;
            }
        }

    }
}

@media (max-width: $screen-sm-max) { /* 991px */

    .page-content {
        border-left: 0;
        border-bottom: 0;

        .page-sidebar-reversed & {
            border-right: 0;
        }
    }

    .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover,
    .page-sidebar {
        .page-sidebar-menu {
            > li {
                > a {
                    border-top: 1px solid $sidebar-menu-deliver-border-color;
                }
            }
        }
    }
}

@media (max-width: $screen-xs-min) { /* 480px */

    body {
        background: lighten($header-bg-color, 13%);
    }

    .page-header.navbar {
        .top-menu {
            background-color: lighten($header-bg-color, 13%);

            .page-header-fixed-mobile & {
                background-color: $header-bg-color;
            }

            .navbar-nav {
                > li.dropdown {
                    .dropdown-toggle {
                        > i {
                            color: lighten($header-top-menu-icon-font-color, 12%);
                        }
                    }
                }
            }
        }
    }
}
